# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

CoCraft is a professional design tool built with a hybrid architecture combining C++ core engine with TypeScript/JavaScript frontend layers. It's a monorepo using pnpm workspaces and turborepo for build orchestration.

## Essential Commands

### Development Setup
```bash
# Install dependencies and setup project
pnpm run init

# Start workspace development server
pnpm run start:workspace

# Start web frontend development
cd packages/frontend/web && pnpm run start:dev
```

### Building
```bash
# Build all packages
pnpm run build

# Build C++ editor core (WebAssembly)
cd packages/common/editor && pnpm run build:wasm

# Build web frontend with optimized C++ core
cd packages/frontend/web && pnpm run build
```

### Testing and Linting
```bash
# Run linting across all packages
pnpm run lint

# Run tests for workspace components
cd packages/common/workspace && pnpm run test

# Run E2E tests for web frontend
cd packages/frontend/web && pnpm run test:e2e-ci
```

## Architecture Overview

### Core System Layers
1. **C++ Editor Core** (`packages/common/editor/`) - TGFX-based native editor engine compiled to WebAssembly
2. **API Bridge** (`packages/common/api/`) - JavaScript ↔ C++ communication layer via Emscripten
3. **Workspace/Workbench** (`packages/common/workspace/`) - Feature-based UI framework built on Tencent Workbench
4. **Frontend Applications** (`packages/frontend/`) - Multi-platform React applications

### Key Architectural Patterns
- **Hybrid Multi-layer**: Performance-critical operations in C++, UI logic in TypeScript
- **Service-Oriented**: Dependency injection with clear service boundaries
- **Event-Driven**: Observer patterns for state synchronization between layers
- **Feature System**: Modular features with config, controllers, and UI components

## Major Components

### C++ Editor Core Modules
- **Bridge**: WebAssembly interface for JavaScript communication
- **Entity**: Node-based scene graph with mixins pattern
- **Render**: TGFX graphics rendering pipeline
- **Vector**: Vector graphics operations and editing tools
- **Text/Textcore**: Advanced typography and text rendering
- **Selection**: Multi-selection management system
- **Viewport**: Canvas navigation and transformation

### JavaScript Services
- **EditorService**: Primary bridge to C++ editor core
- **H2DService**: HTML to Design conversion orchestration
- **DSLToDesignService**: Converts parsed web content to design nodes
- **DesignCollectService**: Extracts and manages design assets (icons, videos)
- **CollabService**: Real-time collaboration via WebSocket

### H2D (HTML to Design) System
1. **Backend Parser** (`packages/backend/plugin/`) uses Playwright for web scraping and asset extraction
2. **Frontend Services** convert parsed data into native design elements
3. **Collection Services** extract reusable assets like icons and videos

## Development Guidelines

### Working with the C++ Core
- C++ code builds to WebAssembly via Emscripten
- Use `packages/common/editor/build.sh` for compilation
- Debug builds include DWARF symbols: `pnpm run build:debug:dwarf`
- Bridge layer handles all JavaScript ↔ C++ communication

### Working with TypeScript Features
- Features use Tencent Workbench framework with dependency injection
- Services are registered via `@Service()` decorator
- State management combines C++ editor state with JavaScript UI state
- Use `EditorService.getCocraft()` to access C++ editor APIs

### Testing Strategy
- Unit tests: Jest for TypeScript, gtest for C++
- Integration tests for API bridge layer
- E2E tests using Playwright for full application workflows
- Coverage reports available via `pnpm run test:cov`

### Package Dependencies
Key dependency relationships:
- `frontend/web` → `workspace` → `api` → `editor`
- `workspace` contains most business logic and UI components
- `editor` is the performance-critical C++ core
- `api` provides the crucial bridge layer

### Build System Notes
- Uses pnpm workspaces with version `workspace:*` for internal dependencies
- Turborepo handles build orchestration and caching
- C++ builds require specific tool chains (see `tools/install.sh`)
- WebAssembly builds support both single-threaded and multi-threaded variants

### Collaboration Features
- Real-time collaboration via WebSocket connections
- Conflict resolution handled at the C++ core level
- State synchronization between editor core and UI layers
- Offline mode support with sync on reconnection