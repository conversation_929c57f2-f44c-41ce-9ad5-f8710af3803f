import { Service } from '@tencent/workbench';
import { CoCraftBridgeAPI } from '@tencent/cocraft-api';
import {
  CallbackParams,
  HtmlParser,
  IhtmlParams,
  IImportData,
  ImageAsset,
  IPageParams,
  IUrlParams,
} from '@tencent/h2d-html-parser';
import { DSLToDesignService } from './dsl-to-design-service';
import { EditorService } from '../editorServices';
import { calcBlankPosition, setMappingFamilyMap } from './utils';
import { DEFAULT_THEME, DEFAULT_VIEWPORT_WIDTH } from './constant';
import { Collect } from './types';
import { DesignCollectService } from './design-collect-service';

@Service()
export class H2DService {
  private cocraft: CoCraftBridgeAPI;

  constructor(
    private dslToDesignService: DSLToDesignService,
    private editorService: EditorService,
    private designCollectService: DesignCollectService,
  ) {
    this.cocraft = this.editorService.getCocraft();
  }

  /**
   * 通过代码创建设计稿
   * @param params
   */
  public async startByCode(params: Omit<IhtmlParams, 'pageParams'> & { pageParams?: Partial<IPageParams> }) {
    const htmlParser = this.getHtmlParser();

    return htmlParser.create({
      html: params.html,
      css: params.css,
      name: params.name,
      pageParams: {
        ...params.pageParams,
        viewport: params.pageParams?.viewport ?? DEFAULT_VIEWPORT_WIDTH,
        theme: params.pageParams?.theme ?? DEFAULT_THEME,
        isUseAutoLayout: params.pageParams?.isUseAutoLayout ?? false,
      },
    });
  }

  /**
   * 通过URL创建设计稿
   * @param params
   */
  public async startByDocJson(params: any, onChange?: (params: CallbackParams) => void) {
    const htmlParser = this.getHtmlParser();
    return htmlParser.createByDocJson(params, onChange);
  }

  /**
   * 通过URL创建设计稿
   * @param params
   */
  public async startByUrl(
    params: Omit<IUrlParams, 'pageParams'> & { pageParams?: Partial<IPageParams> },
    onChange?: (params: CallbackParams) => void,
  ) {
    const htmlParser = this.getHtmlParser();

    return htmlParser.create({
      url: params.url,
      name: params.name ?? '',
      id: params.id,
      pageParams: {
        ...params.pageParams,
        isCreateCollectAssets: true,
        viewport: params.pageParams?.viewport ?? DEFAULT_VIEWPORT_WIDTH,
        theme: params.pageParams?.theme ?? DEFAULT_THEME,
        isUseAutoLayout: params.pageParams?.isUseAutoLayout ?? false,
      },
    }, onChange);
  }

  private getHtmlParser() {
    return new HtmlParser({
      apiUrl: process.env.BASE_AI_URL,
      source: 'codesign',
      getImageHash: (args) => {
        return this.getImageHash(args.data, args.asset);
      },
      getVideoHash: (args) => {
        return this.getVideoHash(args.data);
      },
      createDesign: async ({ importData }: { importData: IImportData }) => {
        await this.createDesign(importData);
        return '';
      },
      createDesignCollect: () => this.designCollectService.start(),
    });
  }

  private async createDesign(importData: IImportData) {
    const frame = importData.model;

    try {
      // await this.cocraft.loadAllPagesAsync();
      // 设置缺失字体的映射
      if (importData.data) {
        setMappingFamilyMap(importData.data.mappingFamily || {});
      }

      frame.name = importData.data.name;

      let { x, y } = calcBlankPosition(this.cocraft);
      let parentBox: FrameNode | null = null;

      // 如果存在 importData.data?.container 需要计算插入节点位置
      if (importData.data?.container?.targetId) {
        const containerNode = (await this.cocraft.getNodeByIdAsync(importData.data.container.targetId)) as FrameNode;
        if (containerNode && containerNode.type === 'FRAME' && importData.data?.container?.optType === 'replace') {
          x = containerNode.x;
          y = containerNode.y;
          containerNode.remove();
        } else if (
          containerNode
          && containerNode.type === 'FRAME'
          && importData.data?.container?.optType === 'append'
        ) {
          containerNode.children.forEach((child) => {
            child.remove();
          });
          parentBox = containerNode;
        }
      }

      const { isCreateCollectAssets } = importData.data;
      if (isCreateCollectAssets) {
        this.designCollectService.clearCollectData();
      }
      await this.dslToDesignService.createDesignNode(
        frame,
        { x, y },
        {
          onGetCollectSvg: (svgDsl) => {
            if (isCreateCollectAssets) {
              this.designCollectService.getCollectData()[Collect.Svg].push(svgDsl);
            }
          },
          onGetCollectVideo: (videoHash) => {
            if (isCreateCollectAssets) {
              this.designCollectService.getCollectData()[Collect.Video].push(videoHash);
            }
          },
        },
        parentBox as FrameNode,
      );

      const frmaeNode = this.cocraft.currentPage.findOne(node => node.name === frame.name);

      if (frmaeNode) {
        // 定位到容器
        this.cocraft.viewport.scrollAndZoomIntoView([frmaeNode]);
      }
      return true;
    } catch (error) {
      console.error(`🚀 ~ createNode ~ ${frame.type} error:`, error);
      return false;
    }
  }

  private async getImageHash(data: Uint8Array, asset: ImageAsset) {
    try {
      let image: Image | null = null;
      if (data) {
        image = this.cocraft.createImage(data);
      } else {
        image = await this.cocraft.createImageAsync(asset.value);
      }

      const { width, height } = await image.getSizeAsync();
      return {
        success: true,
        data: {
          hash: image.hash,
          width,
          height,
        },
      };
    } catch (error) {
      return {
        success: false,
        data: null,
        error,
      };
    }
  }

  private async getVideoHash(data: Uint8Array) {
    try {
      const { hash } = await this.cocraft.createVideoAsync(data);
      return {
        success: true,
        data: hash,
      };
    } catch (error) {
      return {
        success: false,
        data: null,
        error,
      };
    }
  }
}
