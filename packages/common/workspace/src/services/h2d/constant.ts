/**
 * 外间隙
 */
export const OUTER_GAP = 50;
export const PAGE_MARGIN = 200;

export const COLLECT_AREA_WIDTH = 1360;

/**
 * 默认字体
 */
export const DEFAULT_FONT_NAME: FontName = {
  family: 'Source Han Sans CN',
  style: 'Regular',
};

/**
 * 默认视口宽度
 */
export const DEFAULT_VIEWPORT_WIDTH = 1920;

/**
 * 默认主题
 */
export const DEFAULT_THEME = 'light';

export const DEFAULT_LOADING_MASK_SIZE = {
  app: {
    width: 412,
    height: 917,
  },
  pc: {
    width: 1920,
    height: 1080,
  },
};

export const DEFAULT_LOADING_MASK_OPTIONS = {
  fills: [
    {
      type: 'GRADIENT_LINEAR',
      gradientTransform: [
        [
          0,
          1,
          0,
        ],
        [
          -1,
          0,
          1,
        ],
      ],
      gradientStops: [
        {
          color: {
            r: 0.8980392217636108,
            g: 0.9490196108818054,
            b: 0.8039215803146362,
            a: 1,
          },
          position: 0,
          boundVariables: {},
        },
        {
          color: {
            r: 0.8274509906768799,
            g: 0.9450980424880981,
            b: 0.8235294222831726,
            a: 1,
          },
          position: 1,
          boundVariables: {},
        },
      ],
      visible: true,
      blendMode: 'NORMAL',
      opacity: 1,
    },
  ],
};

