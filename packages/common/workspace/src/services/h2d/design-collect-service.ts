import { Service } from '@tencent/workbench';
import { CoCraftBridgeAPI } from '@tencent/cocraft-api';
import { ISvgNode } from '@tencent/h2d-html-parser';
import { EditorService } from '../editorServices';
import { assignObject, calcBlankPosition } from './utils';
import { COLLECT_AREA_WIDTH, DEFAULT_FONT_NAME, PAGE_MARGIN } from './constant';
import { DSLToDesignService } from './dsl-to-design-service';
import { CollectData, Collect, Position } from './types';

@Service()
export class DesignCollectService {
  private cocraft: CoCraftBridgeAPI;
  private collectData: CollectData = {
    [Collect.Svg]: [],
    [Collect.Video]: [],
  };

  constructor(private editorService: EditorService, private dslToDesignService: DSLToDesignService) {
    this.cocraft = this.editorService.getCocraft();
  }

  getCollectData() {
    return this.collectData;
  }

  clearCollectData() {
    this.collectData[Collect.Svg].length = 0;
    this.collectData[Collect.Video].length = 0;
  }

  start() {
    try {
      const pos = calcBlankPosition(this.cocraft);
      this.createCollectArea(Collect.Svg, pos);
      pos.x += COLLECT_AREA_WIDTH + PAGE_MARGIN;
      this.createCollectArea(Collect.Video, pos);
    } finally {
      this.clearCollectData();
    }
  }

  createCollectArea(type: Collect, pos: Position) {
    if (this.collectData[type].length === 0) {
      return;
    }

    const frame = this.cocraft.createFrame();
    frame.x = pos.x;
    frame.y = pos.y;
    frame.name = type === Collect.Svg ? 'SVG Collect' : 'Video Collect';
    frame.fills = [{ type: 'SOLID', color: { r: 1, g: 1, b: 1 } }];
    frame.resize(COLLECT_AREA_WIDTH, 800);

    const headerFrame = this.createHeader(type);
    headerFrame.name = `${frame.name} Header`;
    const bodyFrame = this.createBody(type);
    bodyFrame.name = `${frame.name} Body`;

    frame.resize(COLLECT_AREA_WIDTH, bodyFrame.height + 500);
    frame.appendChild(headerFrame);
    frame.appendChild(bodyFrame);

    this.cocraft.currentPage.appendChild(frame);

    return frame;
  }


  private createHeader(type: Collect) {
    const headerFrame = this.cocraft.createFrame();
    headerFrame.resize(COLLECT_AREA_WIDTH, 214);
    assignObject(headerFrame, {
      x: 0,
      y: 0,
      fills: [{ type: 'SOLID', color: { r: 0.97, g: 0.97, b: 0.97 } }],
    });

    const desc = 'DesignGenie helps you extract design elements from the pages.';
    const title = type === Collect.Svg ? 'Icons' : 'Videos';

    const descriptionText = this.cocraft.createText();
    descriptionText.resize(475, 25);
    assignObject(descriptionText, {
      name: desc,
      x: 128,
      y: 60,
      characters: desc,
      fontName: DEFAULT_FONT_NAME,
      fontSize: 16,
      fills: [{ type: 'SOLID', color: { r: 0, g: 0, b: 0 } }],
    });

    const titleText = this.cocraft.createText();
    titleText.resize(145, 70);
    assignObject(titleText, {
      name: title,
      x: 128,
      y: 100,
      characters: title,
      fontName: { ...DEFAULT_FONT_NAME, style: 'Bold' },
      fontSize: 48,
      fills: [{ type: 'SOLID', color: { r: 0, g: 0, b: 0 } }],
    });

    headerFrame.appendChild(titleText);
    headerFrame.appendChild(descriptionText);

    return headerFrame;
  }

  private createBody(type: Collect) {
    const bodyFrame = this.cocraft.createFrame();
    bodyFrame.resize(1104, 500);
    assignObject(bodyFrame, {
      x: 128,
      y: 342,
      // 以下自动布局属性设置，会报错layoutGrow期望number却得到了string
      // layoutMode: 'HORIZONTAL',
      // layoutPositioning: 'AUTO',
      // layoutSizingHorizontal: 'FIXED',
      // layoutSizingVertical: 'HUG',
      // layoutWrap: 'WRAP',
      // counterAxisAlignContent: 'AUTO',
      // counterAxisAlignItems: 'MIN',
      // counterAxisSizingMode: 'AUTO',
      // counterAxisSpacing: 128,
      // itemSpacing: 128,
      // primaryAxisSizingMode: 'FIXED',
    });

    this.collectData[type].forEach((value) => {
      let itemNode: SceneNode | undefined;
      if (type === Collect.Svg) {
        itemNode = this.createSvgItem(value as ISvgNode);
      } else {
        itemNode = this.createVideoItem(value as string);
      }
      if (itemNode) {
        bodyFrame.appendChild(itemNode);
      }
    });

    return bodyFrame;
  }

  private createSvgItem(svgDsl: ISvgNode) {
    // 生成策略：正方形比例识别为一个icon，其他svg先忽略
    if (svgDsl.width && svgDsl.width === svgDsl.height) {
      const svgItem = this.cocraft.createFrame();
      svgItem.resize(180, 170);
      const svgIcon = this.dslToDesignService.toSvgNode(svgDsl);
      if (svgIcon) {
        const svgIconWidth = 40;
        const svgIconHeight = 40;
        svgIcon.x = (180 - svgIconWidth) / 2;
        svgIcon.y = (170 - svgIconHeight) / 2;
        svgIcon.resize(svgIconWidth, svgIconHeight);
        svgItem.appendChild(svgIcon);

        // 创建 title
        const svgTitle = this.cocraft.createText();
        svgTitle.resize(100, 30);
        svgTitle.characters = `${svgIconWidth} x ${svgIconHeight}`;
        svgTitle.x = 40;
        svgTitle.y = 136;
        svgTitle.fontSize = 26;
        svgTitle.fills = [{ type: 'SOLID', color: { r: 0.125, g: 0.129, b: 0.141 }, opacity: 0.5 }];
        svgItem.appendChild(svgTitle);
        return svgItem;
      }
    }
  }

  private createVideoItem(videoHash: string) {
    const videoItem = this.cocraft.createFrame();
    videoItem.fills = [
      {
        type: 'VIDEO',
        scaleMode: 'FILL',
        videoHash,
      },
    ];

    videoItem.resize(600, 600);
    return videoItem;
  }
}
