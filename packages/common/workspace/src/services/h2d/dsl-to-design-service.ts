import { Service } from '@tencent/workbench';
import { CoCraftBridgeAPI } from '@tencent/cocraft-api';
import { IComponentNode, IDslNode, IFrameNode, IRectangleNode, ISvgNode, ITextNode } from '@tencent/h2d-html-parser';
import { EditorService } from '../editorServices';
import { allPaintsIsBlackSolid, assignObject, rounding } from './utils';
import { DEFAULT_FONT_NAME, PAGE_MARGIN } from './constant';
import { Position } from './types';

@Service()
export class DSLToDesignService {
  private cocraft: CoCraftBridgeAPI;

  constructor(private editorService: EditorService) {
    this.cocraft = this.editorService.getCocraft();
  }

  /**
   * DSLNode 转 DesignNode
   */
  public async createDesignNode(
    node: IDslNode,
    position: Position,
    callbacks: {
      onGetCollectSvg: (svgDsl) => void;
      onGetCollectVideo: (videoHash) => void;
    },
    parentBox?: FrameNode,
  ) {
    return new Promise<void>((resolve) => {
      setTimeout(async () => {
        if (!(node.width >= 0.01 && node.height >= 0.01)) {
          resolve();
          return;
        }

        node?.fills
          ?.forEach((fill) => {
            if (fill.type === 'VIDEO') {
              callbacks.onGetCollectVideo(fill.videoHash);
            }
          });

        if (node.type === 'FRAME') {
          const frameNode = await this.toFrameNode(node);

          if (!frameNode) {
            resolve();
            return frameNode;
          }

          // 设置生成位置&层级
          if (parentBox) {
            frameNode.x = rounding(node.x);
            frameNode.y = rounding(node.y);
            parentBox.appendChild(frameNode);
          } else if (frameNode) {
            if (!position) {
              frameNode.x = rounding(this.cocraft.viewport.bounds.x) + 200;
              frameNode.y = rounding(this.cocraft.viewport.bounds.y) + 200;
            } else {
              frameNode.x = position.x;
              frameNode.y = position.y;
            }
            position.x = frameNode.x + frameNode.width + PAGE_MARGIN;
            position.y = frameNode.y;
            frameNode.clipsContent = false;
            this.cocraft.currentPage.appendChild(frameNode);
          }
          // 设置需要在关联父亲级后才生效的属性
          await this.toFrameNodeAfter(node, frameNode, parentBox);
          // 处理子级内容
          if (node.children) {
            // zIndex 仅在非自动布局下生效
            const children = !['HORIZONTAL', 'VERTICAL'].includes(node.layoutMode)
              ? node.children?.sort((a, b) => {
                if ('$zIndex' in a && '$zIndex' in b) {
                  return a.$zIndex - b.$zIndex;
                }
                return 0;
              })
              : node.children;

            children.forEach((child: IDslNode) => {
              this.createDesignNode(child as IFrameNode, position, callbacks, frameNode);
            });
          }
        } else if (node.type === 'TEXT' && parentBox) {
          const textNode = await this.toTextNode(node as ITextNode);

          if (textNode) {
            parentBox.appendChild(textNode as SceneNode);
          }
        } else if (node.type === 'SVG' && parentBox) {
          const svgNode = this.toSvgNode(node as ISvgNode);

          if (svgNode) {
            callbacks.onGetCollectSvg(node as ISvgNode);
            parentBox.appendChild(svgNode as SceneNode);
          }
        } else if (node.type === 'RECTANGLE' && parentBox) {
          const rectangleNode = await this.toRectangleNode(node as IRectangleNode);

          if (rectangleNode) {
            parentBox.appendChild(rectangleNode as SceneNode);
          }
          if (rectangleNode && parentBox.layoutMode !== 'NONE' && node.layoutPositioning === 'ABSOLUTE') {
            rectangleNode.layoutPositioning = 'ABSOLUTE';
          }
        } else if (node.type === 'COMPONENT' && parentBox) {
          const componentNode = await this.toComponentInstanceNode(node as IComponentNode);

          if (componentNode) {
            parentBox.appendChild(componentNode as SceneNode);
          }
        }

        resolve();
      });
    });
  }

  public async toFrameNodeAfter(node: IFrameNode, frameNode: FrameNode, parentBox?: FrameNode) {
    try {
      // w | h 占满设置
      if (frameNode && parentBox && parentBox.layoutMode !== 'NONE') {
        if (node.constraints.horizontal === 'STRETCH') {
          frameNode.layoutSizingHorizontal = 'FILL';
        }
        if (node.constraints.vertical === 'STRETCH') {
          frameNode.layoutSizingVertical = 'FILL';
        }
        // 绝对定位设置
        if (node.$position === 'absolute') {
          frameNode.layoutPositioning = 'ABSOLUTE';
        }
      }
      // 最大最小值设置
      if ((parentBox && parentBox.layoutMode !== 'NONE') || frameNode.layoutMode !== 'NONE') {
        const { height } = frameNode;
        const { width } = frameNode;
        if (node.maxHeight) frameNode.maxHeight = node.maxHeight;
        if (node.maxWidth) frameNode.maxWidth = node.maxWidth;
        if (node.minHeight) frameNode.minHeight = node.minHeight;
        if (node.minWidth) frameNode.minWidth = node.minWidth;

        frameNode.resize(Math.max(width, frameNode.minWidth || 0), Math.max(height, frameNode.minHeight || 0));
      }
    } catch (e) {
      console.error('🚀 ~ createFigmaNode ~ e:', e);
    }
  }

  public toFrameNode(node: IFrameNode): FrameNode | null {
    try {
      const frame = this.cocraft.createFrame();
      frame.resize(rounding(node.width), rounding(node.height));
      assignObject(frame, {
        opacity: Number.isNaN(node.opacity) ? undefined : node.opacity,
        x: rounding(node.x),
        y: rounding(node.y),
        name: node.name,
        fills: node.fills,
        effects: node.effects,
        clipsContent: !!node.clipsContent,
        relativeTransform: node.relativeTransform,
        constraints: node.constraints,
        layoutMode: node.layoutMode,
        layoutWrap: node.layoutWrap,
        primaryAxisSizingMode: node.primaryAxisSizingMode,
        counterAxisSizingMode: node.counterAxisSizingMode,
        itemSpacing: node.itemSpacing,
        counterAxisSpacing: node.counterAxisSpacing,
        primaryAxisAlignItems: node.primaryAxisAlignItems,
        counterAxisAlignItems: node.counterAxisAlignItems,
        strokesIncludedInLayout: node.strokesIncludedInLayout,
        paddingTop: node.paddingTop,
        paddingRight: node.paddingRight,
        paddingBottom: node.paddingBottom,
        paddingLeft: node.paddingLeft,
        cornerRadius: node.cornerRadius,
        // 有cornerRadius就不设置topLeftRadius等
        topLeftRadius: node.cornerRadius ? undefined : node.topLeftRadius ?? 0,
        topRightRadius: node.cornerRadius ? undefined : node.topRightRadius ?? 0,
        bottomLeftRadius: node.cornerRadius ? undefined : node.bottomLeftRadius ?? 0,
        bottomRightRadius: node.cornerRadius ? undefined : node.bottomRightRadius ?? 0,
      });

      if (Array.isArray(node.strokes) && node.strokes.length > 0) {
        frame.strokes = node.strokes;
        if (node.strokeWeight) {
          frame.strokeWeight = node.strokeWeight;
        } else {
          frame.strokeTopWeight = node.strokeTopWeight ?? 0;
          frame.strokeBottomWeight = node.strokeBottomWeight ?? 0;
          frame.strokeLeftWeight = node.strokeLeftWeight ?? 0;
          frame.strokeRightWeight = node.strokeRightWeight ?? 0;
        }
      }

      return frame;
    } catch (error) {
      console.error('Error creating rectangle:', error);
      return null;
    }
  }

  public toRectangleNode(node: IRectangleNode): RectangleNode | null {
    try {
      const rectangle = this.cocraft.createRectangle();

      rectangle.resize(rounding(node.width), rounding(node.height));

      assignObject(rectangle, {
        opacity: Number.isNaN(node.opacity) ? undefined : node.opacity,
        x: rounding(node.x),
        y: rounding(node.y),
        fills: node.fills,
        relativeTransform: node.relativeTransform,
        cornerRadius: node.cornerRadius,
        // 有cornerRadius就不设置topLeftRadius等
        topLeftRadius: node.cornerRadius ? undefined : node.topLeftRadius ?? 0,
        topRightRadius: node.cornerRadius ? undefined : node.topRightRadius ?? 0,
        bottomLeftRadius: node.cornerRadius ? undefined : node.bottomLeftRadius ?? 0,
        bottomRightRadius: node.cornerRadius ? undefined : node.bottomRightRadius ?? 0,
      });

      if (Array.isArray(node.fills) && node.fills.some(fill => fill.type === 'IMAGE')) {
        rectangle.constraints = { horizontal: 'STRETCH', vertical: 'STRETCH' };
      }

      return rectangle;
    } catch (error) {
      console.error('Error creating rectangle:', error);
      return null;
    }
  }

  public async toTextNode(node: ITextNode): Promise<TextNode | null> {
    try {
      const text = this.cocraft.createText();
      text.resize(rounding(node.width), rounding(node.height));

      const fontName = {
        ...DEFAULT_FONT_NAME,
        // family: node.fontFamily,
        style: node.fontWeight,
      };
      // await this.cocraft.loadFontAsync(fontName);

      assignObject(text, {
        name: node.characters,
        x: rounding(node.x),
        y: rounding(node.y),
        characters: node.characters,
        fills: node.fills,
        fontName,
        textAutoResize: 'WIDTH_AND_HEIGHT',
        textAlignVertical: node.textAlignVertical,
        lineHeight: node.lineHeight || 'AUTO',
        fontSize: node.fontSize,
        textAlignHorizontal: node.textAlignHorizontal,
        textDecoration: node.textDecoration,
      });


      return text;
    } catch (error) {
      console.error('Error creating text:', error);
      return null;
    }
  }

  public toSvgNode(node: ISvgNode): FrameNode | null {
    try {
      // const svgNode = this.cocraft.createNodeFromSvg(node.svg);
      const svgNode = this.cocraft.createFrame();
      svgNode.resize(rounding(node.width), rounding(node.height));

      assignObject(svgNode, {
        name: node.name,
        x: rounding(node.x),
        y: rounding(node.y),
      });

      if (node.layoutMode && node.counterAxisAlignItems && node.primaryAxisAlignItems) {
        svgNode.layoutMode = node.layoutMode;
        svgNode.counterAxisAlignItems = node.counterAxisAlignItems;
        svgNode.primaryAxisAlignItems = node.primaryAxisAlignItems;
      }

      if (node.fills.length > 0) {
        // 遍历节点，找到需要更改颜色的形状
        const vectorShapes = svgNode.findAll(node => node.type === 'VECTOR');
        // 遍历所有 VECTOR 节点并更改它们的颜色
        vectorShapes.forEach((shape) => {
          const currShape = shape as VectorNode;
          /**
           * svg fill 属性有值时，优先级大于 css color
           * 当前形状填充为黑色 SolidFill 时，分两种情况：
           * 1. svg 默认填充
           * 2. svg fill 属性为此颜色
           * 目前两种情况统一使用 node.fills (css color) 覆盖
           **/
          if (currShape.fills !== this.cocraft.mixed && allPaintsIsBlackSolid(currShape.fills)) {
            currShape.fills = node.fills;
          }
          if (allPaintsIsBlackSolid(currShape.strokes)) {
            currShape.strokes = node.fills;
          }
        });
      }
      return svgNode;
    } catch (error) {
      console.error('Error creating svg node:', error);

      return null;
    }
  }

  public async toComponentInstanceNode(node: IComponentNode): Promise<InstanceNode | null> {
    try {
      const page = this.cocraft.root.findOne(n => n.id === node.instanceConfig.pid) as PageNode;
      if (!page) {
        throw new Error(`组件 ${node.instanceConfig.id} 找不到对应的页面`);
      }

      const mainComponent = page.findOne(n => n.id === node.instanceConfig.id) as ComponentNode;

      if (!mainComponent) {
        throw new Error(`${node.instanceConfig.id} 找不到对应的master组件`);
      }
      if (mainComponent.type !== 'COMPONENT') {
        throw new Error(`主组件类型不合法，期望得到COMPONENT类型，实际得到${mainComponent.type}`);
      }

      const instanceNode = mainComponent.createInstance();
      instanceNode.x = rounding(node.x);
      instanceNode.y = rounding(node.y);

      instanceNode.resize(
        node.width && node.width > 0 ? node.width : instanceNode.width,
        node.height && node.height > 0 ? node.height : instanceNode.height,
      );

      // 处理 text resource
      for (const text of node.instanceConfig.texts) {
        try {
          const textNode = instanceNode.findOne(n => n.id.endsWith(text.id.replace('I', ''))) as TextNode;
          if (textNode) {
            const fn = textNode.fontName === this.cocraft.mixed ? DEFAULT_FONT_NAME : textNode.fontName;
            // await this.cocraft.loadFontAsync(fn);
            textNode.characters = text.value;
            textNode.fontName = fn;
          }
        } catch (error) {
          console.error(`节点 ${node.instanceConfig.id} 生成ComponentInstanceNode失败：` + `处理 text resource 失败${error}`);
        }
      }

      // 处理 image resource
      for (const image of node.instanceConfig.imgs) {
        try {
          if (!image.src) {
            continue;
          }

          const imageNode = instanceNode.findOne(n => n.id.endsWith(image.id.replace('I', ''))) as RectangleNode;

          if (imageNode && Array.isArray(imageNode.fills)) {
            const imageFill = imageNode.fills.find(fill => fill.type === 'IMAGE');
            if (imageFill) {
              // TODO: 请求报403
              const temporaryImageNode = await this.cocraft.createImageAsync(image.src);
              const newFills = imageNode.fills.map((fill) => {
                if (fill.type === 'IMAGE') {
                  return {
                    ...fill,
                    imageHash: temporaryImageNode.hash,
                  };
                }
                return fill;
              });
              imageNode.fills = newFills;
            }
          }
        } catch (error) {
          console.error(`节点 ${node.instanceConfig.id} 生成ComponentInstanceNode失败：` + `处理 image resource 失败${error}`);
        }
      }

      return instanceNode;
    } catch (error) {
      console.error(`节点 ${node.instanceConfig.id} 生成ComponentInstanceNode失败：${error}`);

      return null;
    }
  }
}
